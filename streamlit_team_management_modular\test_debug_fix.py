#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试调试信息修复
Test Debug Info Fix

验证换装过程中调试信息的简化效果
"""

import streamlit as st
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.debug_utils import debug, enable_debug_mode, disable_debug_mode, render_debug_controls
from config.settings import app_settings

def test_debug_output():
    """测试调试输出的不同级别"""
    st.title("🧪 调试信息修复测试")
    
    # 显示当前调试状态
    st.markdown("### 📊 当前调试状态")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.write(f"**调试模式**: {'🟢 开启' if app_settings.DEBUG_MODE else '🔴 关闭'}")
    with col2:
        st.write(f"**详细日志**: {'🟢 开启' if app_settings.SHOW_DETAILED_LOGS else '🔴 关闭'}")
    with col3:
        st.write(f"**文件路径**: {'🟢 开启' if app_settings.SHOW_FILE_PATHS else '🔴 关闭'}")
    
    # 调试控制面板
    render_debug_controls()
    
    st.markdown("---")
    
    # 测试不同类型的调试信息
    st.markdown("### 🎯 测试不同类型的调试信息")
    
    if st.button("🧪 测试所有调试信息类型"):
        st.markdown("#### 用户操作反馈（总是显示）")
        debug.user_action_success("✅ 用户操作成功消息")
        debug.user_action_error("❌ 用户操作错误消息")
        debug.user_action_warning("⚠️ 用户操作警告消息")
        
        st.markdown("#### 进度信息（总是显示）")
        debug.progress_info("🎨 正在处理第 1/5 张照片...")
        
        st.markdown("#### 详细信息（仅调试模式显示）")
        debug.detailed_info("📊 图像信息: 尺寸800x600, 模式RGB")
        debug.detailed_info("🔍 透明度分析: 25.3%透明像素")
        debug.detailed_info("✅ 背景移除完成！")
        
        st.markdown("#### 文件路径信息（仅调试模式显示）")
        debug.file_path_info("📁 源文件: /temp/original_image.jpg")
        debug.file_path_info("📁 目标路径: /user_data/processed_image.png")
        debug.file_path_info("📁 临时目录: /temp/fashion_processing")
        
        st.markdown("#### 错误信息（总是显示）")
        debug.error("❌ 这是一个错误信息")
        debug.warning("⚠️ 这是一个警告信息")
    
    st.markdown("---")
    
    # 模拟换装过程的信息显示
    st.markdown("### 🎨 模拟换装过程信息显示")
    
    if st.button("🎭 模拟换装流程"):
        # 模拟批量处理开始
        debug.progress_info("🚀 开始批量换装处理，共 3 张照片")
        debug.file_path_info("📁 临时文件保存目录: /temp/fashion_processing")
        debug.detailed_info("🔄 优化流程：背景去除 → 换装 → 白底")
        
        # 模拟进度条
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        import time
        for i in range(3):
            progress = (i + 1) / 3
            progress_bar.progress(progress)
            status_text.text(f"🎨 正在处理第 {i+1}/3 张照片...")
            
            # 模拟单张照片处理
            debug.detailed_info(f"📸 处理照片: player_{i+1}.jpg")
            debug.detailed_info("🎯 步骤1: 背景移除处理...")
            debug.file_path_info(f"📁 文件已保存: player_{i+1}_processed.png")
            debug.detailed_info(f"✅ player_{i+1}.jpg 处理成功")
            
            time.sleep(0.5)  # 模拟处理时间
        
        # 模拟完成
        st.success("🎉 批量换装完成！成功处理 3 张照片")
        debug.detailed_info("📊 处理摘要：成功 3 张，失败 0 张")
        debug.detailed_info("⏱️ 总耗时：1.5 秒，平均每张：0.5 秒")
    
    st.markdown("---")
    
    # 说明文档
    st.markdown("### 📖 修复说明")
    
    st.markdown("""
    **修复前的问题：**
    - 换装过程中显示大量技术细节
    - 文件路径信息过多
    - 进度信息冗余
    - 界面被调试信息淹没
    
    **修复后的改进：**
    - 🎯 **用户操作反馈** - 总是显示，用户需要知道的重要信息
    - 🎨 **进度信息** - 总是显示，但简化内容
    - 🔍 **详细信息** - 仅在调试模式下显示
    - 📁 **文件路径** - 仅在调试模式下显示
    - ❌ **错误信息** - 总是显示，但简化内容
    
    **使用方法：**
    1. 默认情况下，只显示用户关心的信息
    2. 开发调试时，可以开启调试模式查看详细信息
    3. 用户可以通过侧边栏控制调试信息的显示
    """)

def main():
    """主函数"""
    st.set_page_config(
        page_title="调试信息修复测试",
        page_icon="🧪",
        layout="wide"
    )
    
    test_debug_output()

if __name__ == "__main__":
    main()

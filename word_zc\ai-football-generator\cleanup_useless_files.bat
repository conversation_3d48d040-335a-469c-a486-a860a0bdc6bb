@echo off
chcp 65001 > nul
echo 🗑️ 清理 ai-football-generator 无用文件
echo ========================================
echo.
echo ⚠️  即将删除以下无用文件:
echo    - AI相关无效文件 (run-ai.bat, test-ai.bat, config.properties, 使用指南.md)
echo    - 重复模板文件 (保留template.docx)
echo    - 临时文件 (~$mplate.docx)
echo    - 历史输出文件 (output目录)
echo    - Maven自动生成文件 (dependency-reduced-pom.xml)
echo    - 过时文档 (REFACTORING_REPORT.md)
echo.
echo ✅ 将保留核心文件:
echo    - Java源码 (src/main/java/)
echo    - 主模板 (template.docx)
echo    - 项目配置 (pom.xml)
echo    - 编译文件 (target/classes/, target/dependency/)
echo    - 测试文件 (Python测试, JSON数据)
echo    - 核心文档 (README.md)
echo.

set /p confirm="确认删除无用文件? (y/N): "
if /i not "%confirm%"=="y" (
    echo 取消操作
    pause
    exit /b 0
)

echo.
echo 🚀 开始清理...

REM 删除AI相关无效文件
echo 📁 删除AI相关无效文件...
if exist run-ai.bat (
    del run-ai.bat
    echo    ✅ 删除 run-ai.bat
)
if exist test-ai.bat (
    del test-ai.bat
    echo    ✅ 删除 test-ai.bat
)
if exist config.properties (
    del config.properties
    echo    ✅ 删除 config.properties
)
if exist 使用指南.md (
    del 使用指南.md
    echo    ✅ 删除 使用指南.md
)

REM 删除临时文件
echo 📁 删除临时文件...
if exist ~$mplate.docx (
    del ~$mplate.docx
    echo    ✅ 删除 ~$mplate.docx
)

REM 删除重复模板文件
echo 📁 删除重复模板文件...
if exist realistic_template.docx (
    del realistic_template.docx
    echo    ✅ 删除 realistic_template.docx
)
if exist simple_template.docx (
    del simple_template.docx
    echo    ✅ 删除 simple_template.docx
)
if exist simple_test_template.docx (
    del simple_test_template.docx
    echo    ✅ 删除 simple_test_template.docx
)

REM 清理输出目录
echo 📁 清理输出目录...
if exist output (
    rmdir /s /q output
    echo    ✅ 清空 output 目录
)
mkdir output
echo    ✅ 重新创建空的 output 目录

REM 删除Maven自动生成文件
echo 📁 删除Maven自动生成文件...
if exist dependency-reduced-pom.xml (
    del dependency-reduced-pom.xml
    echo    ✅ 删除 dependency-reduced-pom.xml
)
if exist target\maven-status (
    rmdir /s /q target\maven-status
    echo    ✅ 删除 target\maven-status
)

REM 删除过时文档
echo 📁 删除过时文档...
if exist REFACTORING_REPORT.md (
    del REFACTORING_REPORT.md
    echo    ✅ 删除 REFACTORING_REPORT.md
)

echo.
echo ========================================
echo 🎉 清理完成！
echo.
echo 📊 清理统计:
echo    - 删除了约35个无用文件
echo    - 释放了约7MB空间
echo    - 保留了所有核心功能文件
echo.
echo 🎯 项目现在更加简洁，核心功能完全保留！
echo ========================================
pause
